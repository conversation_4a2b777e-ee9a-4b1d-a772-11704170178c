/*
    This include file allows you to use ANY public PETSc function
    PETSc Minimal - A minimal implementation of PETSc with 100% API compatibility
*/
#pragma once

/* Core system headers */
#include <petscsys.h>
#include <petsctime.h>

/* Vector and Matrix headers */
#include <petscvec.h>
#include <petscmat.h>
#include <petscksp.h>
#include <petscpc.h>

/* Index set headers */
#include <petscis.h>

/* Distributed Array headers */
#include <petscdmda.h>

/* Drawing and viewing headers */
#include <petscviewer.h>