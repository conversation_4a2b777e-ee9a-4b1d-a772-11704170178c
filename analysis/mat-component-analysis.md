# PETSc Mat Component Analysis

## Overview
The Mat component provides the matrix abstraction in PETSc, representing linear operators in various formats including sparse, dense, and specialized types. It manages matrix assembly, mathematical operations, and provides the foundation for all linear solvers in PETSc.

## Core Data Structures

### Mat Object Structure
```c
struct _p_Mat {
  PETSCHEADER(struct _MatOps);
  PetscLayout      rmap, cmap;           // Row and column layouts
  void            *data;                 // Implementation-specific data
  MatFactorType    factortype;           // Factorization type (LU, ILU, etc.)
  PetscBool        assembled;            // Assembly completion flag
  PetscBool        was_assembled;        // Values inserted into assembled matrix
  PetscInt         num_ass;              // Number of assembly cycles
  PetscObjectState nonzerostate;         // Track nonzero pattern changes
  MatInfo          info;                 // Matrix information structure
  InsertMode       insertmode;           // Insert or add mode
  MatStash         stash, bstash;        // Off-processor assembly storage
  MatNullSpace     nullsp;               // Null space
  MatNullSpace     transnullsp;          // Transpose null space
  MatNullSpace     nearnullsp;           // Near null space for multigrid
  Petsc<PERSON>ool        preallocated;         // Preallocation completed
  PetscBool3       symmetric, hermitian; // Symmetry properties
  PetscOffloadMask offloadmask;          // GPU/CPU data location
  // Additional fields for factorization, blocking, etc.
};
```

### Mat Operations Table
The `_MatOps` structure contains function pointers for all matrix operations:

**Core Operations (0-24):**
- setvalues, getrow, restorerow
- mult, multadd, multtranspose, multtransposeadd
- solve, solveadd, solvetranspose, solvetransposeadd
- lufactor, choleskyfactor, sor, transpose
- getinfo, equal, getdiagonal, diagonalscale, norm
- assemblybegin, assemblyend, setoption, zeroentries, zerorows

**Advanced Operations (25-220):**
- Factorization: lufactorsymbolic, lufactornumeric, ilufactor, iccfactor
- Matrix operations: axpy, copy, scale, shift, duplicate
- Submatrix operations: createsubmatrices, getvalues
- Partitioning and ordering: increaseoverlap, getrowij, getcolumnij
- Parallel operations: matmatmult, mattransposematmult, ptap
- Specialized: fdcoloringsetup, setblocksizes, residual

### MatStash Structure (Similar to VecStash)
```c
typedef struct {
  PetscInt     nmax, n, bs;         // Stash sizing
  PetscInt    *rindices, *cindices; // Row and column indices
  PetscScalar *array;               // Stashed values
  MPI_Comm     comm;                // MPI communicator
  // Communication arrays for parallel assembly
  MPI_Request *send_waits, *recv_waits;
  PetscScalar *svalues, *rvalues;
  InsertMode   insertmode;          // Insert or add mode
} MatStash;
```

## Matrix Types and Implementations

### Core Sparse Matrix Types
- **MATAIJ/MATSEQAIJ/MATMPIAIJ**: Compressed Sparse Row (CSR) format
- **MATBAIJ/MATSEQBAIJ/MATMPIBAIJ**: Block compressed sparse row
- **MATSBAIJ/MATSEQSBAIJ/MATMPISBAIJ**: Symmetric block compressed sparse row

### Dense Matrix Types
- **MATDENSE/MATSEQDENSE/MATMPIDENSE**: Dense matrices
- **MATELEMENTAL**: Interface to Elemental library
- **MATSCALAPACK**: Interface to ScaLAPACK

### Specialized Matrix Types
- **MATSHELL**: User-defined matrix operations
- **MATNEST**: Nested block matrices
- **MATCOMPOSITE**: Sum of matrices
- **MATMFFD**: Matrix-free finite difference
- **MATNORMAL**: A^T * A or A * A^T
- **MATSCATTER**: Scatter/gather matrix operations

### GPU Accelerated Types
- **MATAIJCUSPARSE**: CUDA sparse matrices
- **MATAIJHIPSPARSE**: HIP sparse matrices (AMD)
- **MATAIJKOKKOS**: Kokkos portability layer
- **MATAIJVIENNACL**: ViennaCL OpenCL matrices

## AIJ (Compressed Sparse Row) Implementation

### Sequential AIJ Structure (Mat_SeqAIJ)
```c
typedef struct {
  SEQAIJHEADER(PetscScalar);
} Mat_SeqAIJ;

// Where SEQAIJHEADER includes:
PetscBool         roworiented;      // Row-oriented input flag
PetscInt          nz;               // Number of nonzeros
PetscInt         *i;                // Row pointers (size m+1)
PetscInt         *j;                // Column indices (size nz)
PetscScalar      *a;                // Nonzero values (size nz)
PetscInt         *diag;             // Diagonal pointers
PetscInt         *imax;             // Max allocated per row
PetscInt         *ilen;             // Actual length per row
PetscBool         singlemalloc;     // Single allocation flag
PetscInt          maxnz;            // Total allocated nonzeros
```

### Parallel AIJ Structure (Mat_MPIAIJ)
```c
typedef struct {
  Mat A, B;                    // Diagonal and off-diagonal blocks
  PetscMPIInt  size, rank;     // MPI size and rank
  PetscInt     rstart, rend;   // Row ownership range
  PetscInt     cstart, cend;   // Column ownership range
  PetscInt    *garray;         // Global column numbers for B
  PetscInt     ngarray;        // Size of garray
  VecScatter   Mvctx;          // Scatter context for matvec
  // Additional parallel-specific fields
} Mat_MPIAIJ;
```

## Source File Organization

### Core Interface Files
- `src/mat/interface/matrix.c` - Main matrix interface functions
- `src/mat/interface/matreg.c` - Matrix type registration
- `src/mat/interface/matproduct.c` - Matrix-matrix operations
- `src/mat/interface/matnull.c` - Null space operations

### AIJ Implementation Files
**Sequential AIJ:**
- `src/mat/impls/aij/seq/aij.c` - Core sequential AIJ operations
- `src/mat/impls/aij/seq/aijfact.c` - Factorization routines
- `src/mat/impls/aij/seq/matmatmult.c` - Matrix multiplication
- `src/mat/impls/aij/seq/matptap.c` - P^T * A * P operations

**Parallel AIJ:**
- `src/mat/impls/aij/mpi/mpiaij.c` - Core parallel AIJ operations
- `src/mat/impls/aij/mpi/mpimatmatmult.c` - Parallel matrix multiplication
- `src/mat/impls/aij/mpi/mpiptap.c` - Parallel P^T * A * P operations

### Dense Implementation Files
- `src/mat/impls/dense/seq/dense.c` - Sequential dense matrices
- `src/mat/impls/dense/mpi/mpidense.c` - Parallel dense matrices

### Specialized Implementations
- `src/mat/impls/shell/shell.c` - Matrix shell implementation
- `src/mat/impls/nest/matnest.c` - Nested matrices
- `src/mat/impls/mffd/mffd.c` - Matrix-free finite difference

### Utility Components
- `src/mat/utils/matstash.c` - Matrix assembly stash
- `src/mat/utils/freespace.c` - Memory management utilities
- `src/mat/utils/zerorows.c` - Zero rows/columns operations

## Dependencies

### Internal PETSc Dependencies
- **Vec**: Vector operations for matrix-vector products
- **IS**: Index sets for matrix reordering and submatrices
- **PetscLayout**: Matrix distribution and ownership
- **PetscViewer**: Matrix I/O and visualization
- **MatOrdering**: Matrix reordering algorithms
- **MatColoring**: Matrix coloring for finite differences

### External Dependencies
- **MPI**: Parallel matrix operations and communication
- **BLAS Level 2/3**: Matrix-vector and matrix-matrix operations
- **LAPACK**: Dense matrix factorizations
- **Sparse Direct Solvers**: SuperLU, MUMPS, PaStiX, UMFPACK
- **GPU Libraries**: cuSPARSE, hipSPARSE, ViennaCL

### System Dependencies
- **Standard C Library**: Memory management
- **Math Library**: Basic mathematical functions

## Key Algorithms and Patterns

### Matrix Assembly Pattern
1. **Preallocation**: Estimate nonzero pattern using `MatSeqAIJSetPreallocation()`
2. **Value Setting**: Use `MatSetValues()` to insert/add values
3. **Stashing**: Off-processor values stored in stash during parallel assembly
4. **Assembly Begin/End**: Communication and integration of stashed values

### Sparse Matrix-Vector Multiplication (SpMV)
```c
// For AIJ format: y = A * x
for (i = 0; i < m; i++) {
  sum = 0.0;
  for (j = A->i[i]; j < A->i[i+1]; j++) {
    sum += A->a[j] * x[A->j[j]];
  }
  y[i] = sum;
}
```

### Parallel Matrix-Matrix Multiplication
1. **Symbolic Phase**: Determine sparsity pattern of result
2. **Numeric Phase**: Compute numerical values
3. **Communication**: Exchange off-processor data as needed

### Matrix Factorization Patterns
1. **Symbolic Factorization**: Determine fill pattern
2. **Numeric Factorization**: Compute factor values
3. **Solve Phase**: Forward/backward substitution

## Memory Layout Considerations

### CSR Storage Format
- **Row pointers (i)**: Array of size m+1, i[k] points to start of row k
- **Column indices (j)**: Array of size nz, column numbers
- **Values (a)**: Array of size nz, nonzero values
- **Memory requirement**: (m+1) integers + 2*nz (integers + scalars)

### Block Storage Optimization
- **Block CSR**: Store dense blocks instead of individual entries
- **Memory savings**: Reduced index storage for structured problems
- **Performance benefits**: Better cache locality and vectorization

## Critical Function Signatures

### Creation Functions
```c
PetscErrorCode MatCreate(MPI_Comm comm, Mat *mat);
PetscErrorCode MatSetSizes(Mat mat, PetscInt m, PetscInt n, PetscInt M, PetscInt N);
PetscErrorCode MatSetType(Mat mat, MatType type);
PetscErrorCode MatCreateSeqAIJ(MPI_Comm comm, PetscInt m, PetscInt n, PetscInt nz, const PetscInt nnz[], Mat *mat);
PetscErrorCode MatCreateMPIAIJ(MPI_Comm comm, PetscInt m, PetscInt n, PetscInt M, PetscInt N, PetscInt d_nz, const PetscInt d_nnz[], PetscInt o_nz, const PetscInt o_nnz[], Mat *mat);
```

### Assembly Functions
```c
PetscErrorCode MatSetValues(Mat mat, PetscInt m, const PetscInt idxm[], PetscInt n, const PetscInt idxn[], const PetscScalar v[], InsertMode addv);
PetscErrorCode MatAssemblyBegin(Mat mat, MatAssemblyType type);
PetscErrorCode MatAssemblyEnd(Mat mat, MatAssemblyType type);
PetscErrorCode MatZeroEntries(Mat mat);
```

### Mathematical Operations
```c
PetscErrorCode MatMult(Mat mat, Vec x, Vec y);           // y = A * x
PetscErrorCode MatMultAdd(Mat mat, Vec v1, Vec v2, Vec v3); // v3 = v2 + A * v1
PetscErrorCode MatMultTranspose(Mat mat, Vec x, Vec y);   // y = A^T * x
PetscErrorCode MatMatMult(Mat A, Mat B, MatReuse scall, PetscReal fill, Mat *C); // C = A * B
PetscErrorCode MatPtAP(Mat A, Mat P, MatReuse scall, PetscReal fill, Mat *C);    // C = P^T * A * P
```

### Factorization Functions
```c
PetscErrorCode MatLUFactor(Mat mat, IS row, IS col, const MatFactorInfo *info);
PetscErrorCode MatCholeskyFactor(Mat mat, IS perm, const MatFactorInfo *info);
PetscErrorCode MatSolve(Mat mat, Vec b, Vec x);          // Solve A * x = b
PetscErrorCode MatSolveTranspose(Mat mat, Vec b, Vec x); // Solve A^T * x = b
```

### Information and Utility Functions
```c
PetscErrorCode MatGetInfo(Mat mat, MatInfoType flag, MatInfo *info);
PetscErrorCode MatGetSize(Mat mat, PetscInt *m, PetscInt *n);
PetscErrorCode MatGetLocalSize(Mat mat, PetscInt *m, PetscInt *n);
PetscErrorCode MatGetOwnershipRange(Mat mat, PetscInt *rstart, PetscInt *rend);
```

## Performance Characteristics

### Memory Access Patterns
- **SpMV**: Irregular access to vector elements
- **Assembly**: Scattered writes to matrix elements
- **Factorization**: Complex access patterns with fill-in

### Communication Patterns
- **Assembly**: Neighbor-to-neighbor for stash communication
- **SpMV**: Boundary exchange for ghost elements
- **Matrix-Matrix**: Complex all-to-all patterns

### FLOP Counts
- **MatMult**: 2 * nnz flops
- **MatMatMult**: Depends on sparsity patterns
- **LU factorization**: O(n^3) for dense, varies for sparse

## Implementation Strategy for petsc_minimal

### Core Components Required
1. **Mat Object**: Complete _p_Mat structure
2. **MatOps Table**: Essential operations only
3. **AIJ Implementation**: MATSEQAIJ and MATMPIAIJ types
4. **Dense Implementation**: MATSEQDENSE and MATMPDENSE types
5. **Assembly System**: MatStash and assembly routines

### Essential Operations to Implement
1. **Creation**: MatCreate, MatSetSizes, MatSetType
2. **Assembly**: MatSetValues, MatAssemblyBegin/End, MatZeroEntries
3. **Math Ops**: MatMult, MatMultTranspose, MatMultAdd
4. **Info**: MatGetInfo, MatGetSize, MatGetOwnershipRange
5. **Cleanup**: MatDestroy, MatDuplicate

### Simplified Matrix Types
- **MATSEQAIJ**: Sequential compressed sparse row
- **MATMPIAIJ**: Parallel compressed sparse row  
- **MATSEQDENSE**: Sequential dense matrices
- **MATMPIDENSE**: Parallel dense matrices (if MPI support)

### Memory Management Strategy
- Use PetscMalloc/PetscFree for all allocations
- Implement preallocation for efficiency
- Support both single and separate allocations for i, j, a arrays

### Assembly Implementation
- Implement stash system for off-processor values
- Support both INSERT_VALUES and ADD_VALUES modes
- Handle both local and global indexing

## Testing Strategy

### Unit Tests
- Matrix creation and destruction
- Value setting and assembly
- Basic mathematical operations
- Memory management correctness

### Integration Tests
- Matrix-vector products accuracy
- Assembly with various patterns
- Parallel assembly correctness
- Compatibility with Vec operations

### Performance Tests
- SpMV performance benchmarks
- Assembly time measurements
- Memory usage validation

### Validation Against PETSc
- Identical results for all operations
- Same sparsity patterns after assembly
- Compatible file I/O formats

## Binary Compatibility Requirements

### Data Structure Layout
- Exact match of _p_Mat structure
- Same _MatOps function pointer layout
- Identical enum values and constants

### Function Signatures
- Exact parameter lists and types
- Same error code values
- Identical calling conventions

### Memory Layout
- Same CSR storage format
- Compatible assembly patterns
- Identical matrix info structure

This analysis provides the foundation for implementing a minimal but fully compatible Mat component in petsc_minimal, focusing on the most essential matrix types and operations while maintaining full API compatibility.