# PETSc KSP Component Analysis

## Overview
The KSP (Krylov Subspace) component provides iterative linear solvers in PETSc. It manages all Krylov methods and direct solvers, providing a unified interface for solving linear systems Ax = b. The KSP object coordinates between the matrix (A), preconditioner (PC), and solution vectors.

## Core Data Structures

### KSP Object Structure
```c
struct _p_KSP {
  PETSCHEADER(struct _KSPOps);
  DM        dm;                    // Discretization manager
  PetscBool dmAuto, dmActive;      // DM management flags
  
  // Solver parameters
  PetscInt  max_it, min_it;        // Iteration limits
  KSPGuess  guess;                 // Initial guess management
  PetscBool guess_zero;            // Zero initial guess flag
  PetscBool calc_sings, calc_ritz; // Eigenvalue calculation flags
  PCSide    pc_side;               // Preconditioning side (left/right)
  
  // Convergence criteria
  PetscReal rtol, abstol, divtol;  // Relative, absolute, divergence tolerances
  PetscReal rnorm0, rnorm;         // Initial and current residual norms
  KSPConvergedReason reason;       // Convergence reason
  PetscBool errorifnotconverged;   // Error on non-convergence
  
  // Solution and residual tracking
  Vec        vec_sol, vec_rhs;     // User solution and RHS pointers
  PetscReal *res_hist, *err_hist;  // Residual and error history
  size_t     res_hist_len, res_hist_max; // History array sizes
  
  // Convergence monitoring
  PetscErrorCode (*monitor[MAXKSPMONITORS])(KSP, PetscInt, PetscReal, void *);
  void *monitorcontext[MAXKSPMONITORS];
  
  // Linear operators
  Mat amat, pmat;                  // System matrix and preconditioning matrix
  PC  pc;                          // Preconditioner object
  
  // Work vectors and implementation data
  Vec *work;                       // Work vectors
  PetscInt nwork;                  // Number of work vectors
  void *data;                      // Solver-specific data
};
```

### KSP Operations Table
The `_KSPOps` structure contains function pointers for solver operations:
- **setup**: Solver initialization and memory allocation
- **solve**: Main solve routine
- **destroy**: Cleanup and memory deallocation
- **setfromoptions**: Command-line option processing
- **buildsolution**: Build final solution vector
- **buildresidual**: Build final residual vector
- **view**: Solver information display

### Convergence Testing Structure
```c
typedef enum {
  KSP_CONVERGED_RTOL               = 2,  // Relative tolerance achieved
  KSP_CONVERGED_ATOL               = 3,  // Absolute tolerance achieved
  KSP_CONVERGED_ITS                = 4,  // Maximum iterations reached
  KSP_CONVERGED_CG_NEG_CURVE       = 5,  // CG negative curvature
  KSP_CONVERGED_CG_CONSTRAINED     = 6,  // CG constrained step
  KSP_CONVERGED_STEP_LENGTH        = 7,  // Step length achieved
  KSP_CONVERGED_HAPPY_BREAKDOWN    = 8,  // Lucky breakdown occurred
  KSP_DIVERGED_NULL                = -2, // Null divergence
  KSP_DIVERGED_ITS                 = -3, // Maximum iterations exceeded
  KSP_DIVERGED_DTOL                = -4, // Divergence tolerance exceeded
  KSP_DIVERGED_BREAKDOWN           = -5, // Breakdown occurred
  KSP_DIVERGED_BREAKDOWN_BICG      = -6, // BiCG breakdown
  KSP_DIVERGED_NONSYMMETRIC        = -7, // Non-symmetric matrix
  KSP_DIVERGED_INDEFINITE_PC       = -8, // Indefinite preconditioner
  KSP_DIVERGED_NANORINF            = -9, // NaN or Inf detected
  KSP_DIVERGED_INDEFINITE_MAT      = -10 // Indefinite matrix
} KSPConvergedReason;
```

## KSP Types and Implementations

### Basic Iterative Methods
- **KSPRICHARDSON**: Richardson iteration with optional damping
- **KSPCHEBYSHEV**: Chebyshev polynomial acceleration

### Conjugate Gradient Family
- **KSPCG**: Standard Conjugate Gradient for SPD systems
- **KSPPIPECG**: Pipelined CG for improved parallelization
- **KSPFCG**: Flexible CG for varying preconditioners
- **KSPCGNE**: CG Normal Equations (for least squares)

### GMRES Family
- **KSPGMRES**: Generalized Minimal Residual method
- **KSPFGMRES**: Flexible GMRES for varying preconditioners
- **KSPPIPEFGMRES**: Pipelined Flexible GMRES
- **KSPLGMRES**: LGMRES with augmented subspaces

### BiCG Family
- **KSPBICG**: Bi-Conjugate Gradient
- **KSPBCGS**: BiCG Stabilized
- **KSPBCGSL**: BiCG Stabilized with L orthogonalization
- **KSPIBCGS**: Improved BiCG Stabilized
- **KSPFBCGS**: Flexible BiCG Stabilized

### Other Krylov Methods
- **KSPTCQMR**: Transpose-Free Quasi-Minimal Residual
- **KSPTFQMR**: Transpose-Free Quasi-Minimal Residual
- **KSPCGS**: Conjugate Gradient Squared
- **KSPCR**: Conjugate Residual
- **KSPGCR**: Generalized Conjugate Residual

### Least Squares Methods
- **KSPLSQR**: Least Squares QR for rectangular systems
- **KSPCGLS**: Conjugate Gradient Least Squares

### Symmetric Methods
- **KSPMINRES**: Minimal Residual for symmetric indefinite systems
- **KSPSYMMLQ**: Symmetric LQ method

### Direct and Preonly
- **KSPPREONLY**: Apply preconditioner only (direct solvers)
- **KSPNONE**: Equivalent to PREONLY

### Specialized Methods
- **KSPQCG**: Quadratic Conjugate Gradient
- **KSPLCD**: Left Conjugate Direction
- **KSPTSIRM**: Two-Stage Iteration with Recursive Minimization
- **KSPFETIDP**: FETI-DP domain decomposition
- **KSPHPDDM**: Interface to HPDDM library

## Source File Organization

### Core Interface Files
- `src/ksp/ksp/interface/itfunc.c` - Main KSP interface functions
- `src/ksp/ksp/interface/itcreate.c` - KSP creation and setup
- `src/ksp/ksp/interface/itcl.c` - Cleanup and destruction
- `src/ksp/ksp/interface/iterativ.c` - Iterative solver framework

### Solver Implementations

**Conjugate Gradient Methods:**
- `src/ksp/ksp/impls/cg/cg.c` - Standard CG implementation
- `src/ksp/ksp/impls/fcg/fcg.c` - Flexible CG
- `src/ksp/ksp/impls/cr/cr.c` - Conjugate Residual

**GMRES Methods:**
- `src/ksp/ksp/impls/gmres/gmres.c` - Standard GMRES
- `src/ksp/ksp/impls/gmres/fgmres.c` - Flexible GMRES
- `src/ksp/ksp/impls/gmres/lgmres.c` - LGMRES

**BiCG Methods:**
- `src/ksp/ksp/impls/bcgs/bcgs.c` - BiCG Stabilized
- `src/ksp/ksp/impls/bcgsl/bcgsl.c` - BiCG Stabilized(L)
- `src/ksp/ksp/impls/ibcgs/ibcgs.c` - Improved BiCG Stabilized

**Basic Methods:**
- `src/ksp/ksp/impls/rich/rich.c` - Richardson iteration
- `src/ksp/ksp/impls/cheby/cheby.c` - Chebyshev acceleration

**Specialized Methods:**
- `src/ksp/ksp/impls/lsqr/lsqr.c` - Least Squares QR
- `src/ksp/ksp/impls/minres/minres.c` - Minimal Residual
- `src/ksp/ksp/impls/preonly/preonly.c` - Preconditioner only

### Utility Components
- `src/ksp/ksp/interface/iguess.c` - Initial guess management
- `src/ksp/ksp/interface/itres.c` - Residual computation utilities
- `src/ksp/ksp/interface/dmksp.c` - DM-KSP integration

## Key Algorithms

### GMRES Algorithm Structure
```c
// Pseudocode for GMRES iteration
for (restart = 0; restart < max_restarts; restart++) {
  // Initialize
  r0 = b - A*x0;
  beta = ||r0||;
  v[0] = r0 / beta;
  
  // Arnoldi process
  for (j = 0; j < max_k; j++) {
    // Apply preconditioner and matrix
    w = A * M^-1 * v[j];
    
    // Orthogonalize against previous vectors
    for (i = 0; i <= j; i++) {
      H[i][j] = (w, v[i]);
      w = w - H[i][j] * v[i];
    }
    
    H[j+1][j] = ||w||;
    if (H[j+1][j] == 0) break; // Lucky breakdown
    v[j+1] = w / H[j+1][j];
    
    // Update QR factorization and check convergence
    UpdateQR(H, j);
    if (converged) break;
  }
  
  // Build solution
  Solve_UpperTriangular(R, g, y);
  x = x0 + M^-1 * V * y;
}
```

### Conjugate Gradient Algorithm Structure
```c
// Pseudocode for CG iteration
r = b - A*x;
z = M^-1 * r;
p = z;
for (i = 0; i < max_it; i++) {
  q = A * p;
  alpha = (r, z) / (p, q);
  x = x + alpha * p;
  r = r - alpha * q;
  
  if (converged) break;
  
  z_new = M^-1 * r;
  beta = (r, z_new) / (r, z);
  p = z_new + beta * p;
  z = z_new;
}
```

## Dependencies

### Internal PETSc Dependencies
- **Mat**: System matrix and matrix-vector operations
- **PC**: Preconditioner for system conditioning
- **Vec**: Solution and residual vectors
- **IS**: Index sets for domain decomposition methods
- **DM**: Discretization manager for grid-based problems
- **PetscViewer**: Output and monitoring

### External Dependencies
- **MPI**: Parallel communication for iterative methods
- **BLAS Level 1**: Vector operations in inner products and norms
- **LAPACK**: Dense linear algebra for Hessenberg matrices (GMRES)

### Algorithm-Specific Dependencies
- **GMRES**: Requires QR factorization and Givens rotations
- **Eigenvalue methods**: May require ARPACK or SLEPc integration
- **Domain decomposition**: Requires robust subdomain solvers

## Performance Characteristics

### Memory Requirements
- **CG**: 4 vectors (x, r, z, p)
- **GMRES(m)**: (m+1) vectors for Krylov basis + Hessenberg matrix
- **BiCG**: 6-8 vectors depending on variant
- **Storage**: O(n) for most methods, O(nm) for GMRES restart size m

### Communication Patterns
- **Inner products**: Global reductions (MPI_Allreduce)
- **Matrix-vector products**: Neighbor communication for sparse matrices
- **Preconditioning**: Depends on PC type
- **Orthogonalization**: Additional global reductions for GMRES

### Convergence Properties
- **CG**: Optimal for SPD systems, convergence depends on condition number
- **GMRES**: Guaranteed convergence in n steps for non-singular systems
- **BiCG methods**: May breakdown, but often faster than GMRES
- **Preconditioning**: Critical for practical convergence

## Critical Function Signatures

### Core KSP Functions
```c
PetscErrorCode KSPCreate(MPI_Comm comm, KSP *ksp);
PetscErrorCode KSPSetType(KSP ksp, KSPType type);
PetscErrorCode KSPSetOperators(KSP ksp, Mat Amat, Mat Pmat);
PetscErrorCode KSPSetFromOptions(KSP ksp);
PetscErrorCode KSPSetUp(KSP ksp);
PetscErrorCode KSPSolve(KSP ksp, Vec b, Vec x);
PetscErrorCode KSPDestroy(KSP *ksp);
```

### Convergence Control
```c
PetscErrorCode KSPSetTolerances(KSP ksp, PetscReal rtol, PetscReal abstol, 
                               PetscReal dtol, PetscInt maxits);
PetscErrorCode KSPGetIterationNumber(KSP ksp, PetscInt *its);
PetscErrorCode KSPGetConvergedReason(KSP ksp, KSPConvergedReason *reason);
PetscErrorCode KSPGetResidualNorm(KSP ksp, PetscReal *rnorm);
```

### Monitoring and Output
```c
PetscErrorCode KSPMonitorSet(KSP ksp, 
  PetscErrorCode (*monitor)(KSP,PetscInt,PetscReal,void*),
  void *mctx, PetscErrorCode (*monitordestroy)(void**));
PetscErrorCode KSPView(KSP ksp, PetscViewer viewer);
```

### Advanced Configuration
```c
PetscErrorCode KSPSetPCSide(KSP ksp, PCSide side);
PetscErrorCode KSPSetNormType(KSP ksp, KSPNormType normtype);
PetscErrorCode KSPSetInitialGuessNonzero(KSP ksp, PetscBool flg);
PetscErrorCode KSPGMRESSetRestart(KSP ksp, PetscInt restart);
```

## Implementation Strategy for petsc_minimal

### Core Components Required
1. **KSP Object**: Complete _p_KSP structure
2. **KSPOps Table**: Essential operations for each solver type
3. **Basic Solvers**: CG, GMRES, Richardson, PREONLY
4. **Convergence Testing**: Standard convergence criteria
5. **Monitor System**: Basic residual monitoring

### Essential Solver Types to Implement
1. **KSPRICHARDSON**: Simplest iterative method
2. **KSPCG**: Essential for SPD systems
3. **KSPGMRES**: General-purpose solver for non-symmetric systems
4. **KSPPREONLY**: For direct solvers and single PC application
5. **KSPBCGS**: Efficient for non-symmetric systems

### Simplified Algorithm Implementations
- **CG**: Standard algorithm without pipelining optimizations
- **GMRES**: Classical Gram-Schmidt with fixed restart
- **Richardson**: Simple damped iteration
- **BiCGS**: Basic stabilized BiCG without improvements

### Memory Management Strategy
- Pre-allocate work vectors in KSPSetUp()
- Use PetscMalloc/PetscFree for all allocations
- Implement proper cleanup in destroy routines
- Support vector recycling between solves

### Convergence Testing
- Implement standard relative/absolute tolerance testing
- Support divergence detection
- Basic residual norm computation
- Simple iteration count limits

## Testing Strategy

### Unit Tests
- Individual solver convergence on simple problems
- Convergence criteria accuracy
- Memory management correctness
- Monitor system functionality

### Integration Tests
- Solver compatibility with various matrix formats
- Preconditioning integration
- Parallel solver correctness
- Performance benchmarking

### Validation Against PETSc
- Identical convergence behavior
- Same iteration counts for standard problems
- Compatible convergence reasons
- Consistent residual norms

## Binary Compatibility Requirements

### Data Structure Layout
- Exact match of _p_KSP structure
- Same _KSPOps function pointer layout
- Identical enum values for KSPType and KSPConvergedReason

### Function Signatures
- Exact parameter lists and types
- Same error code values
- Identical calling conventions

### Solver Behavior
- Same default parameters
- Compatible convergence criteria
- Identical iteration patterns for deterministic problems

This analysis provides the foundation for implementing essential KSP solvers in petsc_minimal while maintaining full API compatibility with PETSc. The focus is on the most commonly used iterative methods with simplified but correct implementations.