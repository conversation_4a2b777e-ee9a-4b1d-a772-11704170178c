# PETSc Vec Component Analysis

## Overview
The Vec component provides the fundamental vector abstraction in PETSc, representing degrees of freedom for finite element/finite difference functions on a grid. It has more mathematical structure than simple arrays and provides the foundation for all linear algebra operations.

## Core Data Structures

### Vec Object Structure
```c
struct _p_Vec {
  PETSCHEADER(struct _VecOps);
  PetscLayout map;           // Distribution layout across processes
  void       *data;          // Implementation-specific data
  PetscBool   array_gotten;  // Track if array is currently accessed
  VecStash    stash, bstash; // Storage for off-processor values during assembly
  PetscBool   petscnative;   // Native PETSc format flag
  PetscOffloadMask offloadmask; // GPU/CPU data location tracking
  // GPU-related fields when available
  void     *spptr;           // GPU array pointer
  PetscBool boundtocpu;      // CPU binding flag
  PetscBool bindingpropagates; // Binding propagation flag
  char *defaultrandtype;     // Default random type
};
```

### Vec Operations Table
The `_VecOps` structure contains function pointers for all vector operations:
- Basic operations: duplicate, destroy, copy, set, swap
- Mathematical operations: dot, norm, scale, axpy, waxpy, maxpy
- Array access: getarray, restorearray, placearray, replacearray
- Assembly operations: assemblybegin, assemblyend
- Value setting: setvalues, setvaluesblocked, setvalueslocal
- Advanced operations: reciprocal, conjugate, sqrt, abs, exp, log

### VecStash Structure
```c
typedef struct {
  PetscInt     nmax, n, bs;     // Stash sizing and block size
  PetscInt    *idx;             // Global indices
  PetscScalar *array;           // Stashed values
  MPI_Comm     comm;            // MPI communicator
  PetscMPIInt  size, rank;      // MPI size and rank
  // Communication arrays for parallel assembly
  MPI_Request *send_waits, *recv_waits;
  PetscScalar *svalues, *rvalues;
  PetscInt    *sindices, *rindices;
  InsertMode   insertmode;      // Insert or add mode
} VecStash;
```

## Vec Types and Implementations

### Core Vector Types
- **VECSEQ**: Sequential vectors (single process)
- **VECMPI**: Parallel vectors (distributed across processes)
- **VECSTANDARD**: Chooses seq or mpi based on communicator size
- **VECSHARED**: Shared memory vectors for node-local data

### GPU Accelerated Types
- **VECCUDA/VECSEQCUDA/VECMPICUDA**: CUDA GPU acceleration
- **VECHIP/VECSEQHIP/VECMPIHIP**: HIP GPU acceleration (AMD)
- **VECKOKKOS/VECSEQKOKKOS/VECMPIKOKKOS**: Kokkos portability layer
- **VECVIENNACL**: ViennaCL OpenCL acceleration

### Specialized Types
- **VECNEST**: Nested vector composition
- **VECSHARED**: Node-shared memory optimization

## Source File Organization

### Core Interface Files
- `src/vec/vec/interface/vector.c` - Main vector interface functions
- `src/vec/vec/interface/veccreate.c` - Vector creation functions
- `src/vec/vec/interface/rvector.c` - Real-valued vector operations
- `src/vec/vec/interface/vecregall.c` - Vector type registration

### Sequential Implementation
- `src/vec/vec/impls/seq/bvec1.c` - Basic sequential vector operations
- `src/vec/vec/impls/seq/bvec2.c` - Advanced sequential operations
- `src/vec/vec/impls/seq/bvec3.c` - Additional sequential functionality
- `src/vec/vec/impls/seq/dvec2.c` - Double precision operations
- `src/vec/vec/impls/seq/vseqcr.c` - Sequential creation routines

### Parallel (MPI) Implementation
- `src/vec/vec/impls/mpi/pbvec.c` - Parallel basic operations
- `src/vec/vec/impls/mpi/pdvec.c` - Parallel double precision
- `src/vec/vec/impls/mpi/pvec2.c` - Advanced parallel operations
- `src/vec/vec/impls/mpi/vmpicr.c` - MPI creation routines
- `src/vec/vec/impls/mpi/commonmpvec.c` - Common MPI functionality

### GPU Implementations
- `src/vec/vec/impls/seq/cupm/` - CUDA/HIP unified implementation
- `src/vec/vec/impls/mpi/cupm/` - Parallel GPU implementation
- `src/vec/vec/impls/seq/kokkos/` - Kokkos implementation
- `src/vec/vec/impls/seq/seqviennacl/` - ViennaCL implementation

### Utility Components
- `src/vec/vec/utils/` - Vector utility functions
- `src/vec/vec/utils/vecio.c` - I/O operations
- `src/vec/vec/utils/projection.c` - Vector projections
- `src/vec/vec/utils/vinv.c` - Vector inverse operations

## Dependencies

### Internal PETSc Dependencies
- **PetscLayout**: Vector distribution and ownership mapping
- **IS (Index Sets)**: For scatter/gather operations and subvector access
- **PetscViewer**: For vector I/O and visualization
- **PetscRandom**: For random vector initialization
- **PetscOptions**: For command-line option processing

### External Dependencies
- **MPI**: For parallel vector operations and communication
- **BLAS Level 1**: Vector operations (dot, norm, axpy, etc.)
- **CUDA/HIP**: For GPU-accelerated implementations
- **Kokkos**: For performance portability
- **ViennaCL**: For OpenCL acceleration

### System Dependencies
- **Standard C Library**: Memory management, string operations
- **Math Library**: Transcendental functions (sqrt, exp, log)

## Key Algorithms and Patterns

### Vector Assembly Pattern
1. **Local Phase**: Set values using `VecSetValues()`
2. **Stash Phase**: Off-processor values stored in stash
3. **Assembly Begin**: Initiate communication of stashed values
4. **Assembly End**: Complete communication and integration

### Scatter/Gather Operations
1. **Create Scatter Context**: Define source and destination mappings
2. **Begin Scatter**: Initiate non-blocking communication
3. **End Scatter**: Complete communication and data placement

### GPU Memory Management
1. **Offload Mask**: Track data validity on CPU vs GPU
2. **Lazy Transfer**: Transfer data only when needed
3. **Pinned Memory**: Use pinned memory for efficient transfers

## Memory Layout Considerations

### Sequential Vectors
```c
typedef struct {
  VECHEADER;
  PetscScalar *array;      // Contiguous array of values
  PetscScalar *unplaced_array; // For array placement operations
  PetscBool    array_allocated; // Memory ownership flag
} Vec_Seq;
```

### Parallel Vectors
```c
typedef struct {
  VECHEADER;
  PetscInt     localsize;    // Local vector size
  PetscScalar *array;        // Local portion of vector
  PetscBool    array_allocated;
  // Additional parallel-specific fields
} Vec_MPI;
```

## Critical Function Signatures

### Creation Functions
```c
PetscErrorCode VecCreate(MPI_Comm comm, Vec *vec);
PetscErrorCode VecSetSizes(Vec vec, PetscInt n, PetscInt N);
PetscErrorCode VecSetType(Vec vec, VecType type);
PetscErrorCode VecCreateSeq(MPI_Comm comm, PetscInt n, Vec *vec);
PetscErrorCode VecCreateMPI(MPI_Comm comm, PetscInt n, PetscInt N, Vec *vec);
```

### Basic Operations
```c
PetscErrorCode VecDuplicate(Vec vec, Vec *newvec);
PetscErrorCode VecCopy(Vec x, Vec y);
PetscErrorCode VecSet(Vec vec, PetscScalar alpha);
PetscErrorCode VecScale(Vec vec, PetscScalar alpha);
PetscErrorCode VecDestroy(Vec *vec);
```

### Mathematical Operations
```c
PetscErrorCode VecDot(Vec x, Vec y, PetscScalar *result);
PetscErrorCode VecNorm(Vec vec, NormType type, PetscReal *norm);
PetscErrorCode VecAXPY(Vec y, PetscScalar alpha, Vec x);    // y = y + alpha*x
PetscErrorCode VecWAXPY(Vec w, PetscScalar alpha, Vec x, Vec y); // w = y + alpha*x
PetscErrorCode VecPointwiseMult(Vec w, Vec x, Vec y);       // w = x .* y
```

### Array Access
```c
PetscErrorCode VecGetArray(Vec vec, PetscScalar **array);
PetscErrorCode VecRestoreArray(Vec vec, PetscScalar **array);
PetscErrorCode VecGetArrayRead(Vec vec, const PetscScalar **array);
PetscErrorCode VecRestoreArrayRead(Vec vec, const PetscScalar **array);
```

### Value Setting
```c
PetscErrorCode VecSetValues(Vec vec, PetscInt ni, const PetscInt ix[], 
                           const PetscScalar y[], InsertMode iora);
PetscErrorCode VecAssemblyBegin(Vec vec);
PetscErrorCode VecAssemblyEnd(Vec vec);
```

## Performance Characteristics

### Memory Access Patterns
- Sequential access for most operations
- Strided access for multi-component systems
- Random access during assembly phase

### Communication Patterns
- Collective operations for global reductions (dot, norm)
- Point-to-point for scatter/gather operations
- Neighborhood communication for assembly

### FLOP Counts
- VecAXPY: n flops
- VecDot: 2n flops + 1 reduction
- VecNorm: 2n flops + 1 reduction
- VecPointwiseMult: n flops

## Implementation Strategy for petsc_minimal

### Core Components Required
1. **Vec Object**: Complete _p_Vec structure
2. **VecOps Table**: All essential operations
3. **Sequential Implementation**: VECSEQ type
4. **Parallel Implementation**: VECMPI type (if MPI support)
5. **Type Registration**: VecRegister system

### Essential Operations to Implement
1. **Creation**: VecCreate, VecSetSizes, VecSetType
2. **Basic Ops**: VecDuplicate, VecDestroy, VecCopy, VecSet
3. **Math Ops**: VecAXPY, VecDot, VecNorm, VecScale
4. **Array Access**: VecGetArray, VecRestoreArray
5. **Assembly**: VecSetValues, VecAssemblyBegin/End

### Simplified Dependencies
- Remove GPU implementations initially
- Focus on VECSEQ and VECMPI types
- Implement only essential BLAS-1 operations
- Minimal I/O support

### Memory Management Strategy
- Use PetscMalloc/PetscFree for all allocations
- Implement reference counting for shared data
- Support both owned and unowned array data

## Testing Strategy

### Unit Tests
- Basic vector creation and destruction
- Array access and modification
- Mathematical operations accuracy
- Parallel assembly correctness

### Integration Tests
- Compatibility with Mat operations
- Use in KSP solvers
- Performance benchmarking

### Validation Against PETSc
- Identical results for all operations
- Same memory usage patterns
- Compatible file formats

## Binary Compatibility Requirements

### Data Structure Layout
- Exact match of _p_Vec structure
- Same _VecOps function pointer layout
- Identical enum values and constants

### Function Signatures
- Exact parameter lists and types
- Same error code values
- Identical calling conventions

### Memory Layout
- Same array storage patterns
- Compatible scatter/gather formats
- Identical file I/O formats

This analysis provides the foundation for implementing a minimal but fully compatible Vec component in petsc_minimal.