**Task:**
Create a minimal version of PETSc called "petsc_minimal" that includes only essential components while maintaining 100% API compatibility with the full PETSc library. It should be a complete, standalone library that can be used as a drop-in replacement for PETSc in applications that only need the core functionality.

**Requirements:**
- The project mirrors PETSc's organization (src/, include/, config/, etc.)
- Maintain identical header file structure and include paths as PETSc (petsc.h, petscvec.h, petscmat.h, etc.)
- Use CMake as the primary build system with feature parity to PETSc's configure script, without using Python.
- Preserve PETSc's exact naming conventions, function signatures, data structures, and memory layouts for 100% binary compatibility
- Maintain PETSc's version numbering scheme (3.21.2) and API versioning macros
- When implementing a feature, mimic PETSc original implementation as much as possible, do not simplify.
- Before claiming one task successfully complete, alwarys strictly check the code or result files to confirm it is correct.

**Step:**
Complete the project following todo.md.