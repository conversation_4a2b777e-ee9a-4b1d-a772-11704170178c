I want to create a minimal version of PETSc called "petsc_minimal" that includes only essential components while maintaining 100% API compatibility with the full PETSc library. This should be a complete, standalone library that can be used as a drop-in replacement for PETSc in applications that only need the core functionality.

**Project Structure Requirements:**
- Create project in the `petsc_minimal/` directory with structure that mirrors PETSc's organization (src/, include/, config/, etc.)
- Maintain identical header file structure and include paths as PETSc (petsc.h, petscvec.h, petscmat.h, etc.)
- Use CMake as the primary build system with feature parity to PETSc's configure script
- Preserve PETSc's exact naming conventions, function signatures, data structures, and memory layouts for 100% binary compatibility
- Maintain PETSc's version numbering scheme (3.21.2) and API versioning macros

**Core Components to Include (with complete functional implementation):**
- **Vec (vector operations)** - All essential vector types (VecSeq, VecMPI) and operations (VecCreate, VecDestroy, VecSet, VecAXPY, VecDot, VecNorm, VecGetArray, VecRestoreArray, etc.)
- **Mat (matrix operations)** - Core matrix types (MatSeqAIJ, MatMPIAIJ, MatSeqDense) and operations (MatCreate, MatDestroy, MatSetValues, MatMult, MatMultAdd, MatAssemblyBegin/End, MatGetOwnershipRange, etc.)
- **DM (data management)** - Basic DMDA (structured grid) functionality for domain decomposition and ghost point handling
- **IS (index sets)** - Index set operations for parallel data distribution and mapping (ISCreate, ISDestroy, ISGetIndices, ISRestoreIndices, ISLocalToGlobalMapping, etc.)
- **PC (preconditioners)** - The 7 specified preconditioner methods with identical algorithmic behavior
- **KSP (Krylov subspace methods)** - The 5 specified solver methods with identical convergence criteria
- **PetscSys** - Core system functionality including PetscInitialize/Finalize, error handling (SETERRQ, CHKERRQ), logging (PetscInfo), memory management (PetscMalloc/PetscFree), and profiling stubs
- **PetscViewer** - Basic output support (PETSC_VIEWER_STDOUT_WORLD, PETSC_VIEWER_STDOUT_SELF, PetscViewerASCII, PetscViewerBinary)
- **PetscOptions** - Command-line option processing (PetscOptionsGetInt, PetscOptionsGetReal, PetscOptionsGetString, PetscOptionsHasName, etc.)

**Parallelization Support:**
- Sequential (seq) implementation for all vector and matrix types with identical performance charSearched workspaceacteristics
- MPI parallel implementation for all vector and matrix types (VecMPI, MatMPIAIJ) with proper domain decomposition
- Support for both sequential and parallel data structures with identical interfaces and memory layouts
- Maintain PETSc's MPI communicator handling patterns (PETSC_COMM_WORLD, PETSC_COMM_SELF, custom communicators)
- Implement PETSc's parallel assembly patterns (VecAssemblyBegin/End, MatAssemblyBegin/End) with identical synchronization behavior

**Language Support:**
- Complete C interface with all function signatures, parameter types, and calling conventions matching PETSc exactly
- Complete Fortran interface supporting both F77 and F90 calling conventions with identical array handling
- Generate Fortran bindings using PETSc's stub generation approach and maintain identical Fortran include files
- Maintain exact language interoperability including Fortran array indexing differences and string handling

**Data Type Support:**
- PetscReal, PetscScalar, PetscInt, PetscBool, PetscMPIInt types with identical definitions and size requirements
- Support for both real and complex number configurations (--with-scalar-type=real/complex) with identical precision
- Maintain PETSc's precision handling (single/double/quad precision via --with-precision)
- Support PETSc's index type configurations (32-bit/64-bit integers via --with-64-bit-indices)
- Preserve all PETSc type casting and conversion macros

**Platform Support:**
- **Windows**: Windows 7, 10, 11 with MSVC 2013+ and Intel Fortran compiler support
- **Linux**: GNU GCC/gfortran 10+, LLVM/Clang 14+, Intel compiler suite support
- **macOS**: Native Xcode/clang compiler support
- Maintain PETSc's configure-time platform detection, compiler flag selection, and feature testing

**Preconditioner (PC) Methods (complete functional implementations with identical algorithms):**
1. **PCJACOBI** - Diagonal (Jacobi) preconditioner with identical convergence behavior and memory usage
2. **PCBJACOBI** - Block Jacobi preconditioner supporting variable block sizes and identical factorization methods
3. **PCSOR** - Successive Over-Relaxation preconditioner with omega parameter support and identical iteration patterns
4. **PCILU** - Incomplete LU factorization with fill-level, drop tolerance options, and identical sparsity patterns
5. **PCASM** - Additive Schwarz Method with overlap, subdomain solver options, and identical domain decomposition
6. **PCNONE** - Identity preconditioner (no preconditioning) with identical pass-through behavior
7. **PCSHELL** - User-defined preconditioner interface with function pointer support and identical callback mechanisms

**Krylov Solver (KSP) Methods (complete functional implementations with identical algorithms):**
1. **KSPGMRES** - Generalized Minimal Residual method with restart parameter and identical orthogonalization
2. **KSPFGMRES** - Flexible GMRES supporting variable preconditioning with identical memory management
3. **KSPBCGS** - BiCGStab (Biconjugate Gradient Stabilized) method with identical stabilization strategy
4. **KSPRICHARDSON** - Richardson iteration with damping parameter and identical convergence criteria
5. **KSPNONE** - No iterative solver (for direct solve workflows) with identical pass-through behavior

**Strict API Compatibility Requirements:**
- All public header files must be functionally identical to PETSc headers (same declarations, macros, constants)
- Every public function signature, return type, parameter list, and calling convention must match PETSc exactly
- All public data structures must have identical memory layouts, member ordering, alignment requirements, and padding
- All public constants, enums, macros, and typedefs must have identical values and definitions
- Error codes (PETSC_ERR_*) and error handling patterns must be identical including error message formatting
- PETSc object creation/destruction patterns and reference counting must be preserved exactly
- Any existing PETSc application must compile and link without any source code modifications
- Numerical results must be bitwise identical to PETSc for direct methods and within machine precision for iterative methods
- Runtime behavior including memory allocation patterns, convergence criteria, and performance characteristics must match PETSc

**External Dependencies Constraint:**
- **ONLY** use MPI, BLAS, and LAPACK as external dependencies
- **NEVER** require any other external packages or libraries (no HDF5, NetCDF, MUMPS, SuperLU, Hypre, etc.)
- All other functionality must be implemented internally within petsc_minimal
- Use only standard system libraries (libc, libm, pthread where available, libdl for dynamic loading)

**Implementation Approach:**
1. **Codebase Analysis Phase**: Analyze the existing PETSc source code in `petsc-3.21.2/` to understand:
   - Directory structure, file organization patterns, and naming conventions
   - Header file dependencies, include hierarchies, and preprocessor usage patterns
   - Source file dependencies for each required component and their interdependencies
   - Internal object-oriented C patterns, vtable implementations, and function pointer usage
   - Build system structure, configuration options, and feature detection mechanisms
   - Memory management patterns, object lifecycle, and reference counting systems

2. **Dependency Mapping Phase**: Create a comprehensive dependency graph showing:
   - Which specific source files (.c) and headers (.h) are required for each component (Vec, Mat, KSP, PC, DM, IS, etc.)
   - Internal dependencies between components and shared utility functions
   - External library dependencies (BLAS, LAPACK, MPI) and their exact usage patterns
   - Conditional compilation patterns, feature flags, and configuration-dependent code paths
   - Minimum viable file set that maintains full functionality for specified components

3. **Extraction and Implementation Phase**: 
   - Extract minimal set of source files while preserving all internal implementation patterns
   - Maintain PETSc's complex object-oriented C implementation patterns and inheritance hierarchies
   - Preserve function pointer tables, virtual method dispatch, and polymorphic behavior exactly
   - Keep identical initialization sequences, finalization cleanup, and error handling paths
   - Implement the same configure-time and compile-time option handling with identical defaults
   - Ensure all internal data structures maintain exact memory layouts, padding, and alignment

4. **Build System Phase**: Create CMake-based build system that:
   - Mirrors PETSc's configuration options, feature detection, and compiler testing exactly
   - Generates identical compiler flags, preprocessor definitions, and linking options
   - Supports the same installation layout, directory structure, and file permissions as PETSc
   - Maintains compatibility with PETSc's pkg-config files and CMake find modules
   - Provides identical configure-time options (--with-mpi, --with-blas-lapack, --with-debugging, etc.)

**Deliverables:**
1. **Analysis Report**: Complete analysis of PETSc codebase structure, dependencies, and implementation patterns for required components
2. **Dependency Map**: Detailed dependency graph showing exactly which files, functions, and code sections to include/exclude with rationale
3. **Build System**: CMake-based build system with feature parity to PETSc's configure script and identical configuration options
4. **Implementation**: Complete petsc_minimal library with all specified components and identical API surface
5. **Verification Suite**: Test suite demonstrating that existing PETSc applications compile, link, and produce numerically identical results
6. **Documentation**: Installation guide, API compatibility notes, migration instructions, and performance comparison

**Success Criteria:**
- Existing PETSc applications must compile without any source code changes
- Numerical results must be identical (within machine precision for iterative methods)
- Library size should be significantly smaller than full PETSc (target: <40% of full PETSc binary size)
- Build time should be at least 50% faster than full PETSc
- All specified components must pass PETSc's own test suite for those components
- Memory usage should be comparable or lower than full PETSc for equivalent operations

**Initial Task**: Start by analyzing the PETSc codebase structure in the existing `petsc-3.21.2/` directory to understand the organization, dependencies, and implementation patterns. Focus on identifying the specific source files (.c), headers (.h), and build configuration files required for Vec, Mat, KSP, PC, DM, IS, and PetscSys components. Create a detailed inventory of required files and their interdependencies, including file sizes, lines of code, and complexity metrics to estimate the scope of the minimal implementation. Generate a comprehensive report showing which files can be excluded and which must be retained or modified.

**Specific Actions to Take:**
1. Examine the directory structure of `petsc-3.21.2/` to understand the overall organization
2. Identify the core header files in `include/` directory that define the public API for each component
3. Map the source files in `src/` directory to their corresponding components (vec/, mat/, ksp/, pc/, dm/, sys/, etc.)
4. Analyze the build system files (configure.py, makefiles, CMakeLists.txt if present) to understand configuration options
5. Create a dependency matrix showing which source files depend on which other files
6. Estimate the code reduction potential by calculating total lines of code for required vs. excluded components
7. Document any circular dependencies or complex interdependencies that may complicate extraction
8. Identify shared utility functions and data structures that multiple components depend on
9. Note any platform-specific or compiler-specific code that needs special handling
10. Produce a detailed report with recommendations for the minimal implementation approach