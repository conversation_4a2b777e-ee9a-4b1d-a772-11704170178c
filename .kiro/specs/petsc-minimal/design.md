# Design Document

## Overview

The petsc_minimal project creates a streamlined version of PETSc that maintains 100% API compatibility while including only essential components. The design follows PETSc's established architectural patterns, object-oriented C implementation, and modular structure while significantly reducing the codebase size and external dependencies.

Based on analysis of the PETSc 3.21.2 codebase, the design preserves PETSc's core architectural principles:
- Object-oriented C implementation with function pointer tables (vtables)
- Modular component architecture with clear separation of concerns
- Consistent error handling and memory management patterns
- MPI-aware parallel computing abstractions
- Configuration-driven build system with extensive platform support

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        APP[User Applications]
    end
    
    subgraph "PETSc Minimal API Layer"
        VEC[Vec - Vectors]
        MAT[Mat - Matrices] 
        KSP[KSP - Linear Solvers]
        PC[PC - Preconditioners]
        DM[DM - Data Management]
        IS[IS - Index Sets]
    end
    
    subgraph "Core System Layer"
        SYS[PetscSys - Core System]
        OPT[PetscOptions - Configuration]
        VIEW[PetscViewer - I/O]
        ERR[Error Handling]
        MEM[Memory Management]
        LOG[Logging & Profiling]
    end
    
    subgraph "External Dependencies"
        MPI[MPI - Message Passing]
        BLAS[BLAS - Basic Linear Algebra]
        LAPACK[LAPACK - Linear Algebra]
    end
    
    APP --> VEC
    APP --> MAT
    APP --> KSP
    APP --> PC
    APP --> DM
    APP --> IS
    
    VEC --> SYS
    MAT --> SYS
    KSP --> PC
    KSP --> MAT
    PC --> MAT
    DM --> VEC
    DM --> MAT
    IS --> SYS
    
    SYS --> MPI
    SYS --> BLAS
    SYS --> LAPACK
    
    SYS --> OPT
    SYS --> VIEW
    SYS --> ERR
    SYS --> MEM
    SYS --> LOG
```

### Directory Structure

The petsc_minimal project mirrors PETSc's directory organization:

```
petsc_minimal/
├── CMakeLists.txt                 # Main CMake build configuration
├── include/                       # Public header files (API identical to PETSc)
│   ├── petsc.h                   # Main include file
│   ├── petscvec.h                # Vector API
│   ├── petscmat.h                # Matrix API
│   ├── petscksp.h                # KSP solver API
│   ├── petscpc.h                 # Preconditioner API
│   ├── petscdm.h                 # Data management API
│   ├── petscis.h                 # Index set API
│   ├── petscsys.h                # System API
│   ├── petscoptions.h            # Options API
│   ├── petscviewer.h             # Viewer API
│   └── petsc/                    # Private headers
│       └── private/              # Internal implementation headers
├── src/                          # Source code implementation
│   ├── sys/                      # Core system functionality
│   │   ├── objects/              # Object management
│   │   ├── memory/               # Memory management
│   │   ├── error/                # Error handling
│   │   ├── logging/              # Logging and profiling
│   │   └── utils/                # Utility functions
│   ├── vec/                      # Vector implementations
│   │   ├── vec/                  # Vector interface and implementations
│   │   │   ├── interface/        # Vector interface functions
│   │   │   └── impls/            # Vector implementations (seq, mpi)
│   │   └── is/                   # Index set implementations
│   ├── mat/                      # Matrix implementations
│   │   ├── interface/            # Matrix interface functions
│   │   └── impls/                # Matrix implementations (aij, dense)
│   ├── ksp/                      # Linear solver implementations
│   │   ├── ksp/                  # KSP interface and implementations
│   │   │   ├── interface/        # KSP interface functions
│   │   │   └── impls/            # KSP implementations (gmres, bcgs, etc.)
│   │   └── pc/                   # Preconditioner implementations
│   │       ├── interface/        # PC interface functions
│   │       └── impls/            # PC implementations (jacobi, ilu, etc.)
│   └── dm/                       # Data management implementations
│       └── impls/                # DM implementations (dmda)
├── config/                       # Build configuration
│   ├── CMake/                    # CMake modules and functions
│   └── BuildSystem/              # Build system utilities
├── lib/                          # Generated libraries
├── bin/                          # Generated executables and utilities
└── tests/                        # Test suite
    ├── unit/                     # Unit tests
    └── integration/              # Integration tests
```

## Components and Interfaces

### Core System (PetscSys)

**Purpose**: Provides fundamental system services including initialization, error handling, memory management, and logging.

**Key Components**:
- **Initialization/Finalization**: `PetscInitialize()`, `PetscFinalize()`
- **Error Handling**: `SETERRQ()`, `CHKERRQ()`, error code definitions
- **Memory Management**: `PetscMalloc()`, `PetscFree()`, `PetscCalloc()`
- **Logging**: `PetscInfo()`, profiling stubs
- **Object Management**: Reference counting, object lifecycle management

**Implementation Strategy**: Extract core files from `src/sys/` including:
- `src/sys/objects/` - Object management and reference counting
- `src/sys/memory/` - Memory allocation and tracking
- `src/sys/error/` - Error handling and reporting
- `src/sys/logging/` - Basic logging infrastructure

### Vector Component (Vec)

**Purpose**: Provides distributed and sequential vector operations for linear algebra computations.

**Supported Types**:
- **VecSeq**: Sequential vectors for single-process computations
- **VecMPI**: Parallel vectors with MPI-based distribution

**Key Operations**:
- Creation/Destruction: `VecCreate()`, `VecDestroy()`, `VecSetType()`
- Data Access: `VecGetArray()`, `VecRestoreArray()`, `VecSet()`
- Mathematical Operations: `VecAXPY()`, `VecDot()`, `VecNorm()`, `VecScale()`
- Parallel Operations: `VecAssemblyBegin()`, `VecAssemblyEnd()`

**Implementation Strategy**: Extract from `src/vec/vec/`:
- `interface/` - Core vector interface functions
- `impls/seq/` - Sequential vector implementation
- `impls/mpi/` - MPI parallel vector implementation

### Matrix Component (Mat)

**Purpose**: Provides sparse and dense matrix storage and operations for linear systems.

**Supported Types**:
- **MatSeqAIJ**: Sequential sparse matrices (Compressed Sparse Row format)
- **MatMPIAIJ**: Parallel sparse matrices with row-wise distribution
- **MatSeqDense**: Sequential dense matrices

**Key Operations**:
- Creation/Destruction: `MatCreate()`, `MatDestroy()`, `MatSetType()`
- Assembly: `MatSetValues()`, `MatAssemblyBegin()`, `MatAssemblyEnd()`
- Operations: `MatMult()`, `MatMultAdd()`, `MatTranspose()`
- Properties: `MatGetOwnershipRange()`, `MatGetSize()`, `MatGetLocalSize()`

**Implementation Strategy**: Extract from `src/mat/`:
- `interface/` - Core matrix interface functions
- `impls/aij/seq/` - Sequential AIJ implementation
- `impls/aij/mpi/` - MPI parallel AIJ implementation
- `impls/dense/seq/` - Sequential dense implementation

### Linear Solver Component (KSP)

**Purpose**: Provides Krylov subspace methods for solving linear systems Ax = b.

**Supported Methods**:
- **KSPGMRES**: Generalized Minimal Residual method with restart
- **KSPFGMRES**: Flexible GMRES for variable preconditioning
- **KSPBCGS**: BiCGStab (Biconjugate Gradient Stabilized)
- **KSPRICHARDSON**: Richardson iteration with damping
- **KSPNONE**: No iterative solver (direct solve)

**Key Operations**:
- Setup: `KSPCreate()`, `KSPSetType()`, `KSPSetOperators()`
- Solving: `KSPSolve()`, `KSPSetTolerances()`
- Monitoring: `KSPGetIterationNumber()`, `KSPGetResidualNorm()`

**Implementation Strategy**: Extract from `src/ksp/ksp/`:
- `interface/` - KSP interface functions
- `impls/gmres/` - GMRES implementation
- `impls/bcgs/` - BiCGStab implementation
- `impls/rich/` - Richardson implementation
- `impls/preonly/` - Direct solve implementation

### Preconditioner Component (PC)

**Purpose**: Provides preconditioning methods to accelerate iterative solver convergence.

**Supported Methods**:
- **PCJACOBI**: Diagonal (Jacobi) preconditioner
- **PCBJACOBI**: Block Jacobi with variable block sizes
- **PCSOR**: Successive Over-Relaxation with omega parameter
- **PCILU**: Incomplete LU factorization with fill levels
- **PCASM**: Additive Schwarz Method with overlap
- **PCNONE**: Identity preconditioner
- **PCSHELL**: User-defined preconditioner interface

**Key Operations**:
- Setup: `PCCreate()`, `PCSetType()`, `PCSetOperators()`
- Application: `PCApply()`, `PCApplyTranspose()`
- Configuration: Method-specific parameter setting functions

**Implementation Strategy**: Extract from `src/ksp/pc/impls/`:
- `jacobi/` - Jacobi preconditioner
- `bjacobi/` - Block Jacobi preconditioner
- `sor/` - SOR preconditioner
- `factor/` - ILU factorization (subset)
- `asm/` - Additive Schwarz method
- `none/` - Identity preconditioner
- `shell/` - User-defined preconditioner

### Data Management Component (DM)

**Purpose**: Provides structured grid management and domain decomposition for finite difference/element methods.

**Supported Types**:
- **DMDA**: Distributed Arrays for structured grids

**Key Operations**:
- Creation: `DMDACreate1d()`, `DMDACreate2d()`, `DMDACreate3d()`
- Grid Management: `DMDAGetCorners()`, `DMDAGetGhostCorners()`
- Vector Operations: `DMCreateGlobalVector()`, `DMCreateLocalVector()`
- Data Access: `DMDAVecGetArray()`, `DMDAVecRestoreArray()`

**Implementation Strategy**: Extract from `src/dm/impls/da/`:
- Core DMDA implementation for structured grids
- Ghost point management and communication
- Local-to-global mapping functionality

### Index Set Component (IS)

**Purpose**: Provides index set operations for parallel data distribution and mapping.

**Key Operations**:
- Creation: `ISCreate()`, `ISCreateGeneral()`, `ISCreateStride()`
- Access: `ISGetIndices()`, `ISRestoreIndices()`, `ISGetSize()`
- Mapping: `ISLocalToGlobalMappingCreate()`, `ISLocalToGlobalMappingApply()`

**Implementation Strategy**: Extract from `src/vec/is/`:
- Basic index set implementations
- Local-to-global mapping functionality
- Parallel index set operations

### Options System (PetscOptions)

**Purpose**: Provides command-line and configuration file option processing.

**Key Operations**:
- Retrieval: `PetscOptionsGetInt()`, `PetscOptionsGetReal()`, `PetscOptionsGetString()`
- Testing: `PetscOptionsHasName()`, `PetscOptionsGetBool()`
- Management: `PetscOptionsSetValue()`, `PetscOptionsClear()`

### Viewer System (PetscViewer)

**Purpose**: Provides basic output and visualization capabilities.

**Supported Types**:
- **PETSC_VIEWER_STDOUT_WORLD**: Standard output for all processes
- **PETSC_VIEWER_STDOUT_SELF**: Standard output for individual processes
- **PetscViewerASCII**: ASCII text output
- **PetscViewerBinary**: Binary data output

## Data Models

### Object-Oriented C Implementation

PETSc uses a sophisticated object-oriented approach in C with function pointer tables (vtables) for polymorphism. Each major component follows this pattern:

```c
// Example: Vector object structure
struct _p_Vec {
  PETSCHEADER(struct _VecOps);  // Common object header with vtable
  PetscLayout     map;          // Parallel layout information
  PetscScalar    *array;        // Data storage
  PetscInt        n, N;         // Local and global sizes
  PetscBool       assembled;    // Assembly state
  // Additional type-specific data
};

// Vector operations vtable
struct _VecOps {
  PetscErrorCode (*duplicate)(Vec, Vec*);
  PetscErrorCode (*view)(Vec, PetscViewer);
  PetscErrorCode (*destroy)(Vec);
  PetscErrorCode (*norm)(Vec, NormType, PetscReal*);
  PetscErrorCode (*dot)(Vec, Vec, PetscScalar*);
  // Additional operation function pointers
};
```

### Memory Layout Compatibility

All data structures maintain identical memory layouts to PETSc:
- Object headers with vtable pointers in identical positions
- Member variable ordering and alignment preserved
- Padding and structure packing identical to PETSc
- Reference counting and object lifecycle patterns preserved

### Type System

The type system preserves PETSc's exact definitions:

```c
// Scalar and precision types
typedef double PetscReal;        // or float for single precision
typedef double PetscScalar;      // or complex for complex numbers
typedef int PetscInt;            // or long for 64-bit indices
typedef int PetscMPIInt;
typedef enum { PETSC_FALSE, PETSC_TRUE } PetscBool;

// Error codes (identical values to PETSc)
typedef int PetscErrorCode;
#define PETSC_SUCCESS                0
#define PETSC_ERR_MEM               55
#define PETSC_ERR_ARG_WRONG         62
// Additional error codes...
```

## Error Handling

### Error Code System

Maintains PETSc's exact error handling patterns:
- Identical error codes and values
- Same error propagation mechanisms
- Consistent error message formatting
- Stack trace functionality (simplified)

### Error Handling Macros

Preserves all PETSc error handling macros:

```c
#define CHKERRQ(ierr) \
  do { \
    if (PetscUnlikely(ierr)) { \
      return PetscError(PETSC_COMM_SELF, __LINE__, PETSC_FUNCTION_NAME, \
                        __FILE__, ierr, PETSC_ERROR_REPEAT, " "); \
    } \
  } while (0)

#define SETERRQ(comm, ierr, ...) \
  return PetscError(comm, __LINE__, PETSC_FUNCTION_NAME, __FILE__, \
                    ierr, PETSC_ERROR_INITIAL, __VA_ARGS__)
```

## Testing Strategy

### Unit Testing Framework

**Test Organization**:
- Component-level unit tests for each major component
- API compatibility tests ensuring identical behavior
- Numerical accuracy tests comparing results with PETSc
- Memory management and error handling tests

**Test Categories**:

1. **API Compatibility Tests**:
   - Function signature verification
   - Return value consistency
   - Error code compatibility
   - Memory layout verification

2. **Numerical Accuracy Tests**:
   - Vector operations (dot products, norms, AXPY)
   - Matrix operations (multiplication, assembly)
   - Solver convergence (identical iteration counts)
   - Preconditioner effectiveness

3. **Parallel Functionality Tests**:
   - MPI communication patterns
   - Domain decomposition correctness
   - Ghost point handling
   - Parallel assembly operations

4. **Performance Regression Tests**:
   - Memory usage monitoring
   - Operation timing comparisons
   - Scalability verification

### Integration Testing

**PETSc Test Suite Integration**:
- Run subset of PETSc's own test suite against petsc_minimal
- Verify identical numerical results for supported components
- Test compilation of existing PETSc applications without modification

**Continuous Integration**:
- Multi-platform testing (Linux, macOS, Windows)
- Multiple compiler testing (GCC, Clang, MSVC, Intel)
- Various configuration testing (debug/optimized, real/complex, 32/64-bit indices)

### Verification Methodology

**Binary Compatibility Verification**:
- Header file comparison tools
- ABI compatibility checking
- Symbol table verification
- Memory layout validation

**Numerical Verification**:
- Bitwise identical results for direct methods
- Machine precision tolerance for iterative methods
- Convergence behavior matching
- Residual norm comparison

The testing strategy ensures that petsc_minimal can serve as a true drop-in replacement for PETSc while maintaining all compatibility guarantees specified in the requirements.